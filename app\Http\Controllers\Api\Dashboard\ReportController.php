<?php

namespace App\Http\Controllers\Api\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Ticket;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\TicketsExport; // Will create this export class later
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * Instantiate a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:sanctum');
        $this->middleware('can:App.Http.Controllers.Api.Dashboard.ReportController');
    }

    /**
     * Get ticket reports based on filters.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTicketReports(Request $request)
    {
        $request->validate([
            'period' => 'sometimes|in:daily,weekly,monthly,quarterly,annually',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $query = Ticket::query()->with(['user', 'department', 'priority', 'status', 'labels', 'concerns.department']); // Added concerns.department

        $period = $request->input('period');
        $startDate = $request->input('start_date') ? Carbon::parse($request->input('start_date'))->startOfDay() : null;
        $endDate = $request->input('end_date') ? Carbon::parse($request->input('end_date'))->endOfDay() : null;

        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        } elseif ($period) {
            switch ($period) {
                case 'daily':
                    $query->whereDate('created_at', Carbon::today());
                    break;
                case 'weekly':
                    $query->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]);
                    break;
                case 'monthly':
                    $query->whereMonth('created_at', Carbon::now()->month)->whereYear('created_at', Carbon::now()->year);
                    break;
                case 'quarterly':
                    $currentQuarter = Carbon::now()->quarter;
                    $query->whereRaw('QUARTER(created_at) = ?', [$currentQuarter])
                          ->whereYear('created_at', Carbon::now()->year);
                    break;
                case 'annually':
                    $query->whereYear('created_at', Carbon::now()->year);
                    break;
            }
        }
        // Default to fetching all if no period or specific dates are given, or adjust as needed.
        // For reports, usually a period or date range is expected.

        $tickets = $query->orderBy('created_at', 'desc')->paginate(100); // Paginate for API response

        return response()->json($tickets);
    }

    /**
     * Download ticket report as Excel.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\JsonResponse
     */
    public function downloadTicketReport(Request $request)
    {
        $request->validate([
            'period' => 'sometimes|in:daily,weekly,monthly,quarterly,annually',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        // This logic should mirror getTicketReports for data fetching but without pagination for export
        $query = Ticket::query()->with(['user', 'department', 'priority', 'status', 'labels', 'concerns.department']);

        $period = $request->input('period');
        $startDate = $request->input('start_date') ? Carbon::parse($request->input('start_date'))->startOfDay() : null;
        $endDate = $request->input('end_date') ? Carbon::parse($request->input('end_date'))->endOfDay() : null;

        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        } elseif ($period) {
            switch ($period) {
                case 'daily':
                    $query->whereDate('created_at', Carbon::today());
                    break;
                case 'weekly':
                    $query->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]);
                    break;
                case 'monthly':
                    $query->whereMonth('created_at', Carbon::now()->month)->whereYear('created_at', Carbon::now()->year);
                    break;
                case 'quarterly':
                    $currentQuarter = Carbon::now()->quarter;
                    $query->whereRaw('QUARTER(created_at) = ?', [$currentQuarter])
                          ->whereYear('created_at', Carbon::now()->year);
                    break;
                case 'annually':
                    $query->whereYear('created_at', Carbon::now()->year);
                    break;
            }
        } else {
            // If no period or date range, perhaps default to current month or prevent download
            // For this example, let's default to current month if nothing specified
             $query->whereMonth('created_at', Carbon::now()->month)->whereYear('created_at', Carbon::now()->year);
        }

        $tickets = $query->orderBy('created_at', 'desc')->get();
        
        $fileName = 'ticket_report_' . ($period ? $period.'_' : '') . Carbon::now()->format('Ymd_His') . '.xlsx';

        $exportData = new TicketsExport($tickets);

        return Excel::create($fileName, function($excel) use ($exportData) {
            $exportData->build($excel);
        })->download('xlsx');
    }
}
