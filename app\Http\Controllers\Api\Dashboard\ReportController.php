<?php

namespace App\Http\Controllers\Api\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Ticket;
use App\Models\Department;
use App\Models\Status;
use App\Models\Priority;
use App\Models\User;
use App\Models\TicketConcern;
use App\Models\CondoLocation;
// use Maatwebsite\Excel\Facades\Excel;
// use App\Exports\TicketsExport;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class ReportController extends Controller
{
    /**
     * Instantiate a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:sanctum');
        $this->middleware('can:App.Http.Controllers.Api.Dashboard.ReportController');
    }

    /**
     * Get filter options for reports.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFilterOptions()
    {
        $user = Auth::user();

        // Get departments based on user permissions
        $departments = $user->userRole->id === 1
            ? Department::all()
            : $user->departments;

        // Get agents/users with dashboard access
        $agents = User::whereHas('userRole', function($query) {
            $query->where('dashboard_access', 1);
        })->get();

        // Get ticket concerns based on user's departments
        $concerns = $user->userRole->id === 1
            ? TicketConcern::with('department')->where('status', 1)->get()
            : TicketConcern::with('department')->where('status', 1)
                ->whereIn('department_id', $user->departments->pluck('id'))
                ->get();

        return response()->json([
            'departments' => $departments->map(function($dept) {
                return ['id' => $dept->id, 'name' => $dept->name];
            }),
            'statuses' => Status::all()->map(function($status) {
                return ['id' => $status->id, 'name' => $status->name, 'color' => $status->color];
            }),
            'priorities' => Priority::all()->map(function($priority) {
                return ['id' => $priority->id, 'name' => $priority->name, 'value' => $priority->value];
            }),
            'agents' => $agents->map(function($agent) {
                return ['id' => $agent->id, 'name' => $agent->name, 'email' => $agent->email];
            }),
            'concerns' => $concerns->map(function($concern) {
                return [
                    'id' => $concern->id,
                    'name' => $concern->name,
                    'department_id' => $concern->department_id,
                    'department_name' => $concern->department ? $concern->department->name : null
                ];
            }),
            'condo_locations' => CondoLocation::all()->map(function($location) {
                return ['id' => $location->id, 'name' => $location->name];
            })
        ]);
    }

    /**
     * Get ticket reports based on filters.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTicketReports(Request $request)
    {
        $request->validate([
            'period' => 'sometimes|in:daily,weekly,monthly,quarterly,annually',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status_ids' => 'nullable|array',
            'status_ids.*' => 'integer|exists:statuses,id',
            'priority_ids' => 'nullable|array',
            'priority_ids.*' => 'integer|exists:priorities,id',
            'department_ids' => 'nullable|array',
            'department_ids.*' => 'integer|exists:departments,id',
            'agent_ids' => 'nullable|array',
            'agent_ids.*' => 'integer|exists:users,id',
            'concern_ids' => 'nullable|array',
            'concern_ids.*' => 'integer|exists:ticket_concerns,id',
            'condo_location_ids' => 'nullable|array',
            'condo_location_ids.*' => 'integer|exists:condo_locations,id',
            'search' => 'nullable|string|max:255',
            'per_page' => 'nullable|integer|min:10|max:100'
        ]);

        $user = Auth::user();
        $query = Ticket::query()->with([
            'user.condoLocation',
            'department',
            'priority',
            'status',
            'labels',
            'concern.department',
            'agent',
            'condoLocation'
        ]);

        // Apply user department restrictions if not admin
        if ($user->userRole->id !== 1) {
            $userDepartmentIds = $user->departments->pluck('id');
            $query->where(function($q) use ($userDepartmentIds, $user) {
                $q->whereIn('department_id', $userDepartmentIds)
                  ->orWhere('agent_id', $user->id)
                  ->orWhere('user_id', $user->id);
            });
        }

        // Apply date filters
        $period = $request->input('period');
        $startDate = $request->input('start_date') ? Carbon::parse($request->input('start_date'))->startOfDay() : null;
        $endDate = $request->input('end_date') ? Carbon::parse($request->input('end_date'))->endOfDay() : null;

        if ($period) {
            switch ($period) {
                case 'daily':
                    $query->whereDate('created_at', Carbon::today());
                    break;
                case 'weekly':
                    $query->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]);
                    break;
                case 'monthly':
                    $query->whereMonth('created_at', Carbon::now()->month)
                          ->whereYear('created_at', Carbon::now()->year);
                    break;
                case 'quarterly':
                    $quarter = Carbon::now()->quarter;
                    $year = Carbon::now()->year;
                    $startOfQuarter = Carbon::createFromDate($year, ($quarter - 1) * 3 + 1, 1)->startOfMonth();
                    $endOfQuarter = $startOfQuarter->copy()->addMonths(2)->endOfMonth();
                    $query->whereBetween('created_at', [$startOfQuarter, $endOfQuarter]);
                    break;
                case 'annually':
                    $query->whereYear('created_at', Carbon::now()->year);
                    break;
            }
        } elseif ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        } elseif ($startDate) {
            $query->where('created_at', '>=', $startDate);
        } elseif ($endDate) {
            $query->where('created_at', '<=', $endDate);
        }

        // Apply additional filters
        if ($request->filled('status_ids')) {
            $query->whereIn('status_id', $request->input('status_ids'));
        }

        if ($request->filled('priority_ids')) {
            $query->whereIn('priority_id', $request->input('priority_ids'));
        }

        if ($request->filled('department_ids')) {
            $query->whereIn('department_id', $request->input('department_ids'));
        }

        if ($request->filled('agent_ids')) {
            $query->whereIn('agent_id', $request->input('agent_ids'));
        }

        if ($request->filled('concern_ids')) {
            $query->whereIn('concern_id', $request->input('concern_ids'));
        }

        if ($request->filled('condo_location_ids')) {
            $query->whereIn('condo_location_id', $request->input('condo_location_ids'));
        }

        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('subject', 'LIKE', '%'.$search.'%')
                  ->orWhereHas('ticketReplies', function($subQ) use ($search) {
                      $subQ->where('body', 'LIKE', '%'.$search.'%');
                  })
                  ->orWhereHas('user', function($subQ) use ($search) {
                      $subQ->where('name', 'LIKE', '%'.$search.'%')
                           ->orWhere('email', 'LIKE', '%'.$search.'%');
                  });
            });
        }

        $perPage = $request->input('per_page', 25);
        $tickets = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return response()->json($tickets);
    }

    /**
     * Download ticket report as Excel.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\JsonResponse
     */
    public function downloadTicketReport(Request $request)
    {
        $request->validate([
            'period' => 'sometimes|in:daily,weekly,monthly,quarterly,annually',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status_ids' => 'nullable|array',
            'status_ids.*' => 'integer|exists:statuses,id',
            'priority_ids' => 'nullable|array',
            'priority_ids.*' => 'integer|exists:priorities,id',
            'department_ids' => 'nullable|array',
            'department_ids.*' => 'integer|exists:departments,id',
            'agent_ids' => 'nullable|array',
            'agent_ids.*' => 'integer|exists:users,id',
            'concern_ids' => 'nullable|array',
            'concern_ids.*' => 'integer|exists:ticket_concerns,id',
            'condo_location_ids' => 'nullable|array',
            'condo_location_ids.*' => 'integer|exists:condo_locations,id',
            'search' => 'nullable|string|max:255'
        ]);

        $user = Auth::user();
        $query = Ticket::query()->with([
            'user.condoLocation',
            'department',
            'priority',
            'status',
            'labels',
            'concern.department',
            'agent',
            'condoLocation'
        ]);

        // Apply user department restrictions if not admin
        if ($user->userRole->id !== 1) {
            $userDepartmentIds = $user->departments->pluck('id');
            $query->where(function($q) use ($userDepartmentIds, $user) {
                $q->whereIn('department_id', $userDepartmentIds)
                  ->orWhere('agent_id', $user->id)
                  ->orWhere('user_id', $user->id);
            });
        }

        // Apply same filters as getTicketReports method
        $period = $request->input('period');
        $startDate = $request->input('start_date') ? Carbon::parse($request->input('start_date'))->startOfDay() : null;
        $endDate = $request->input('end_date') ? Carbon::parse($request->input('end_date'))->endOfDay() : null;

        if ($period) {
            switch ($period) {
                case 'daily':
                    $query->whereDate('created_at', Carbon::today());
                    break;
                case 'weekly':
                    $query->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]);
                    break;
                case 'monthly':
                    $query->whereMonth('created_at', Carbon::now()->month)
                          ->whereYear('created_at', Carbon::now()->year);
                    break;
                case 'quarterly':
                    $quarter = Carbon::now()->quarter;
                    $year = Carbon::now()->year;
                    $startOfQuarter = Carbon::createFromDate($year, ($quarter - 1) * 3 + 1, 1)->startOfMonth();
                    $endOfQuarter = $startOfQuarter->copy()->addMonths(2)->endOfMonth();
                    $query->whereBetween('created_at', [$startOfQuarter, $endOfQuarter]);
                    break;
                case 'annually':
                    $query->whereYear('created_at', Carbon::now()->year);
                    break;
            }
        } elseif ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        } elseif ($startDate) {
            $query->where('created_at', '>=', $startDate);
        } elseif ($endDate) {
            $query->where('created_at', '<=', $endDate);
        }

        // Apply additional filters
        if ($request->filled('status_ids')) {
            $query->whereIn('status_id', $request->input('status_ids'));
        }

        if ($request->filled('priority_ids')) {
            $query->whereIn('priority_id', $request->input('priority_ids'));
        }

        if ($request->filled('department_ids')) {
            $query->whereIn('department_id', $request->input('department_ids'));
        }

        if ($request->filled('agent_ids')) {
            $query->whereIn('agent_id', $request->input('agent_ids'));
        }

        if ($request->filled('concern_ids')) {
            $query->whereIn('concern_id', $request->input('concern_ids'));
        }

        if ($request->filled('condo_location_ids')) {
            $query->whereIn('condo_location_id', $request->input('condo_location_ids'));
        }

        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('subject', 'LIKE', '%'.$search.'%')
                  ->orWhereHas('ticketReplies', function($subQ) use ($search) {
                      $subQ->where('body', 'LIKE', '%'.$search.'%');
                  })
                  ->orWhereHas('user', function($subQ) use ($search) {
                      $subQ->where('name', 'LIKE', '%'.$search.'%')
                           ->orWhere('email', 'LIKE', '%'.$search.'%');
                  });
            });
        }

        $tickets = $query->orderBy('created_at', 'desc')->get();

        if ($tickets->isEmpty()) {
            return response()->json(['message' => 'No tickets found for the selected criteria.'], 404);
        }

        // Temporary: Return JSON data instead of Excel file until Excel package is fixed
        return response()->json([
            'message' => 'Excel export temporarily disabled. Showing data as JSON.',
            'data' => $tickets->map(function($ticket) {
                return [
                    'id' => $ticket->id,
                    'uuid' => $ticket->uuid,
                    'subject' => $ticket->subject,
                    'status' => $ticket->status->name ?? 'N/A',
                    'priority' => $ticket->priority->name ?? 'N/A',
                    'department' => $ticket->department->name ?? 'N/A',
                    'agent' => $ticket->agent->name ?? 'Unassigned',
                    'user' => $ticket->user->name ?? 'N/A',
                    'condo_location' => $ticket->condoLocation->name ?? $ticket->user->condoLocation->name ?? 'N/A',
                    'created_at' => $ticket->created_at->format('Y-m-d H:i:s'),
                    'updated_at' => $ticket->updated_at->format('Y-m-d H:i:s'),
                ];
            }),
            'total' => $tickets->count()
        ]);
    }
}
