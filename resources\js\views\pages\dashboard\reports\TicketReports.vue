<template>
    <div>
        <div class="flex flex-col lg:flex-row lg:items-center justify-between">
            <div class="mb-2 lg:mb-0">
                <h1 class="text-2xl font-semibold">
                    {{ $t('Ticket Reports') }}
                </h1>
                <div class="text-sm text-slate-500 dark:text-slate-400">
                    {{ $t('Generate and download ticket reports') }}
                </div>
            </div>
        </div>
        <div class="mt-8">
            <div class="bg-white dark:bg-slate-800 shadow-md rounded-lg p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 items-end">
                    <div>
                        <label for="period" class="form-label">{{ $t('Time Period') }}</label>
                        <select id="period" v-model="filters.period" class="form-input">
                            <option v-for="period in timePeriods" :key="period.value" :value="period.value">
                                {{ $t(period.text) }}
                            </option>
                        </select>
                    </div>
                    <div v-if="showCustomDatePickers">
                        <label for="start_date" class="form-label">{{ $t('Start Date') }}</label>
                        <input type="date" id="start_date" v-model="filters.start_date" class="form-input">
                    </div>
                    <div v-if="showCustomDatePickers">
                        <label for="end_date" class="form-label">{{ $t('End Date') }}</label>
                        <input type="date" id="end_date" v-model="filters.end_date" class="form-input">
                    </div>
                </div>
                <div class="flex justify-start space-x-3">
                    <button
                        @click="fetchReport"
                        class="btn btn-primary"
                        :disabled="loading"
                        :class="{'cursor-not-allowed': loading}"
                    >
                        <vue-element-loading :active="loading && !reportData" spinner="spinner" color="#FFF" />
                        {{ $t('Generate Report') }}
                    </button>
                    <button
                        @click="downloadReport"
                        class="btn btn-success"
                        :disabled="loading"
                        :class="{'cursor-not-allowed': loading}"
                    >
                        <vue-element-loading :active="loading && reportData" spinner="spinner" color="#FFF" />
                        {{ $t('Download Report (XLSX)') }}
                    </button>
                </div>

                <!-- Optional: Display fetched report data here -->
                <!--
                <div v-if="reportData && reportData.data && reportData.data.length > 0" class="mt-6">
                    <h3 class="text-lg font-semibold mb-2">{{ $t('Report Preview (First 100 entries)') }}</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
                            <thead class="bg-slate-50 dark:bg-slate-700">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">{{ $t('ID') }}</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">{{ $t('Subject') }}</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">{{ $t('User') }}</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">{{ $t('Status') }}</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">{{ $t('Created At') }}</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-slate-800 divide-y divide-slate-200 dark:divide-slate-700">
                                <tr v-for="ticket in reportData.data" :key="ticket.id">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-slate-200">{{ ticket.id }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-slate-200">{{ ticket.subject }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-slate-200">{{ ticket.user ? ticket.user.name : (ticket.guest_email || 'N/A') }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-slate-200">{{ ticket.status ? ticket.status.name : 'N/A' }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-slate-200">{{ $filters.moment(ticket.created_at, 'YYYY-MM-DD HH:mm') }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div v-if="reportData && reportData.data && reportData.data.length === 0" class="mt-6">
                    <p>{{ $t('No tickets found for the selected period.') }}</p>
                </div>
                -->
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "TicketReports",
    metaInfo() {
        return {
            title: this.$i18n.t('Ticket Reports')
        }
    },
    data() {
        return {
            loading: false,
            reportData: null,
            filters: {
                period: 'monthly', // Default period
                start_date: '',
                end_date: '',
            },
            timePeriods: [
                { value: 'daily', text: 'Daily' },
                { value: 'weekly', text: 'Weekly' },
                { value: 'monthly', text: 'Monthly' },
                { value: 'quarterly', text: 'Quarterly' },
                { value: 'annually', text: 'Annually' },
                { value: 'custom', text: 'Custom Range' },
            ],
            showCustomDatePickers: false,
        };
    },
    watch: {
        'filters.period'(newVal) {
            this.showCustomDatePickers = newVal === 'custom';
            if (newVal !== 'custom') {
                this.filters.start_date = '';
                this.filters.end_date = '';
            }
        }
    },
    methods: {
        fetchReport() {
            this.loading = true;
            this.reportData = null;
            let params = {};
            if (this.filters.period === 'custom') {
                if (!this.filters.start_date || !this.filters.end_date) {
                    this.$notify({type: 'error', text: this.$i18n.t('Please select both start and end dates for custom range.')});
                    this.loading = false;
                    return;
                }
                params.start_date = this.filters.start_date;
                params.end_date = this.filters.end_date;
            } else {
                params.period = this.filters.period;
            }

            axios.get('/api/dashboard/reports/tickets', { params })
                .then(response => {
                    this.reportData = response.data;
                    // For now, we are focusing on download. Displaying data can be added later.
                    if (response.data && response.data.data && response.data.data.length === 0) {
                         this.$notify({type: 'info', text: this.$i18n.t('No tickets found for the selected period.')});
                    } else if (response.data && response.data.data && response.data.data.length > 0) {
                         this.$notify({type: 'success', text: this.$i18n.t('Report data fetched. You can now download it.')});
                    }
                })
                .catch(error => {
                    console.error("Error fetching report:", error);
                    this.$notify({type: 'error', text: this.$i18n.t('Error fetching report.')});
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        downloadReport() {
            this.loading = true;
            let params = {};
            if (this.filters.period === 'custom') {
                if (!this.filters.start_date || !this.filters.end_date) {
                    this.$notify({type: 'error', text: this.$i18n.t('Please select both start and end dates for custom range.')});
                    this.loading = false;
                    return;
                }
                params.start_date = this.filters.start_date;
                params.end_date = this.filters.end_date;
            } else {
                params.period = this.filters.period;
            }

            axios.get('/api/dashboard/reports/tickets/download', { params, responseType: 'blob' })
                .then(response => {
                    const url = window.URL.createObjectURL(new Blob([response.data]));
                    const link = document.createElement('a');
                    link.href = url;
                    let fileName = 'ticket_report.xlsx';
                    const contentDisposition = response.headers['content-disposition'];
                    if (contentDisposition) {
                        const fileNameMatch = contentDisposition.match(/filename="?(.+)"?/i);
                        if (fileNameMatch.length === 2)
                            fileName = fileNameMatch[1];
                    }
                    link.setAttribute('download', fileName);
                    document.body.appendChild(link);
                    link.click();
                    link.remove();
                    window.URL.revokeObjectURL(url);
                    this.$notify({type: 'success', text: this.$i18n.t('Report download started.')});
                })
                .catch(error => {
                    console.error("Error downloading report:", error);
                     this.$notify({type: 'error', text: this.$i18n.t('Error downloading report. Check if there is data for the selected period.')});
                })
                .finally(() => {
                    this.loading = false;
                });
        }
    },
    mounted() {
        // Fetch initial report for default period if needed, or wait for user interaction
        // this.fetchReport();
    }
};
</script>
