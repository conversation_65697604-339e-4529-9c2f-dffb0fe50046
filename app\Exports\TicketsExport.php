<?php

namespace App\Exports;

use App\Models\Ticket;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\Exportable; // For maatwebsite/excel v3, not strictly needed for v1 but good practice
use Illuminate\Support\Collection;

// For Maatwebsite/Excel v1.x, we don't typically use these FromCollection, WithHeadings interfaces directly in the same way as v3.
// Instead, we use the Excel::create method and build the sheet.
// However, to keep the controller logic cleaner with `new TicketsExport($tickets)`,
// this class will act as a wrapper that can be used with a custom Excel::download setup if needed,
// or we adjust the controller to use the v1.x specific syntax.

// Given the controller uses `new TicketsExport($tickets)`, this class needs to be compatible.
// For v1.x, this usually means the class itself doesn't do much, and the logic is in the controller
// or a dedicated service. Let's make this class prepare data for the v1 style.

class TicketsExport
{
    use Exportable; // Keep for potential future upgrade path, though not used by v1 directly

    protected $tickets;

    public function __construct(Collection $tickets)
    {
        $this->tickets = $tickets;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return $this->tickets->map(function ($ticket) {
            return [
                'ID' => $ticket->id,
                'Subject' => $ticket->subject,
                'User' => $ticket->user ? $ticket->user->name : 'N/A',
                'Email' => $ticket->user ? $ticket->user->email : ($ticket->guest_email ?: 'N/A'),
                'Department' => $ticket->department ? $ticket->department->name : 'N/A',
                'Concern' => $ticket->concerns && $ticket->concerns->first() ? $ticket->concerns->first()->name : 'N/A', // Assuming a ticket has one primary concern for simplicity
                'Concern Department' => $ticket->concerns && $ticket->concerns->first() && $ticket->concerns->first()->department ? $ticket->concerns->first()->department->name : 'N/A',
                'Status' => $ticket->status ? $ticket->status->name : 'N/A',
                'Priority' => $ticket->priority ? $ticket->priority->name : 'N/A',
                'Created At' => $ticket->created_at ? $ticket->created_at->format('Y-m-d H:i:s') : 'N/A',
                'Updated At' => $ticket->updated_at ? $ticket->updated_at->format('Y-m-d H:i:s') : 'N/A',
                'Agent' => $ticket->agent ? $ticket->agent->name : 'N/A',
                'Labels' => $ticket->labels->isNotEmpty() ? $ticket->labels->pluck('name')->implode(', ') : '',
            ];
        });
    }

    public function headings(): array
    {
        return [
            'ID',
            'Subject',
            'User',
            'Email',
            'Department',
            'Concern',
            'Concern Department',
            'Status',
            'Priority',
            'Created At',
            'Updated At',
            'Agent',
            'Labels',
        ];
    }

    // This method would be called by Excel::create in v1.x style
    public function build($excel)
    {
        $excel->sheet('Tickets', function ($sheet) {
            $sheet->fromArray($this->collection()->toArray(), null, 'A1', true, true);
            // Set headings explicitly if not using fromArray's auto-heading or if WithHeadings isn't picked up by v1
            // $sheet->prependRow(1, $this->headings()); // Alternative way to set headings
        });
    }
}
