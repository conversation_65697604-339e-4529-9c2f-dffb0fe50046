{"/js/app.js": "/js/app.js", "/css/app.css": "/css/app.css", "/js/skins/content/dark/content.css": "/js/skins/content/dark/content.css", "/js/skins/content/dark/content.min.css": "/js/skins/content/dark/content.min.css", "/js/skins/content/default/content.css": "/js/skins/content/default/content.css", "/js/skins/content/default/content.min.css": "/js/skins/content/default/content.min.css", "/js/skins/content/document/content.css": "/js/skins/content/document/content.css", "/js/skins/content/document/content.min.css": "/js/skins/content/document/content.min.css", "/js/skins/content/writer/content.css": "/js/skins/content/writer/content.css", "/js/skins/content/writer/content.min.css": "/js/skins/content/writer/content.min.css", "/js/skins/ui/oxide/content.css": "/js/skins/ui/oxide/content.css", "/js/skins/ui/oxide/content.inline.css": "/js/skins/ui/oxide/content.inline.css", "/js/skins/ui/oxide/content.inline.min.css": "/js/skins/ui/oxide/content.inline.min.css", "/js/skins/ui/oxide/content.min.css": "/js/skins/ui/oxide/content.min.css", "/js/skins/ui/oxide/content.mobile.css": "/js/skins/ui/oxide/content.mobile.css", "/js/skins/ui/oxide/content.mobile.min.css": "/js/skins/ui/oxide/content.mobile.min.css", "/js/skins/ui/oxide/fonts/tinymce-mobile.woff": "/js/skins/ui/oxide/fonts/tinymce-mobile.woff", "/js/skins/ui/oxide/skin.css": "/js/skins/ui/oxide/skin.css", "/js/skins/ui/oxide/skin.min.css": "/js/skins/ui/oxide/skin.min.css", "/js/skins/ui/oxide/skin.mobile.css": "/js/skins/ui/oxide/skin.mobile.css", "/js/skins/ui/oxide/skin.mobile.min.css": "/js/skins/ui/oxide/skin.mobile.min.css", "/js/skins/ui/oxide/skin.shadowdom.css": "/js/skins/ui/oxide/skin.shadowdom.css", "/js/skins/ui/oxide/skin.shadowdom.min.css": "/js/skins/ui/oxide/skin.shadowdom.min.css", "/js/skins/ui/oxide-dark/content.css": "/js/skins/ui/oxide-dark/content.css", "/js/skins/ui/oxide-dark/content.inline.css": "/js/skins/ui/oxide-dark/content.inline.css", "/js/skins/ui/oxide-dark/content.inline.min.css": "/js/skins/ui/oxide-dark/content.inline.min.css", "/js/skins/ui/oxide-dark/content.min.css": "/js/skins/ui/oxide-dark/content.min.css", "/js/skins/ui/oxide-dark/content.mobile.css": "/js/skins/ui/oxide-dark/content.mobile.css", "/js/skins/ui/oxide-dark/content.mobile.min.css": "/js/skins/ui/oxide-dark/content.mobile.min.css", "/js/skins/ui/oxide-dark/fonts/tinymce-mobile.woff": "/js/skins/ui/oxide-dark/fonts/tinymce-mobile.woff", "/js/skins/ui/oxide-dark/skin.css": "/js/skins/ui/oxide-dark/skin.css", "/js/skins/ui/oxide-dark/skin.min.css": "/js/skins/ui/oxide-dark/skin.min.css", "/js/skins/ui/oxide-dark/skin.mobile.css": "/js/skins/ui/oxide-dark/skin.mobile.css", "/js/skins/ui/oxide-dark/skin.mobile.min.css": "/js/skins/ui/oxide-dark/skin.mobile.min.css", "/js/skins/ui/oxide-dark/skin.shadowdom.css": "/js/skins/ui/oxide-dark/skin.shadowdom.css", "/js/skins/ui/oxide-dark/skin.shadowdom.min.css": "/js/skins/ui/oxide-dark/skin.shadowdom.min.css"}