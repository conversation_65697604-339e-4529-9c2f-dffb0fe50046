{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.3|^8.0|^8.1", "ext-json": "*", "ext-fileinfo": "*", "dacoto/laravel-setenv": "^2.0", "fideloper/proxy": "^4.2", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^7.0", "kkomelin/laravel-translatable-string-exporter": "^1.11", "laravel/framework": "^8.0", "laravel/sanctum": "^2.6", "laravel/tinker": "^2.0", "maatwebsite/excel": "^3.1", "pusher/pusher-php-server": "^7.2", "sentry/sentry-laravel": "^2.1", "tucker-eric/eloquentfilter": "^2.4"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.8", "facade/ignition": "^2.3.6", "fzaninotto/faker": "^1.9.1", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^5.0", "phpunit/phpunit": "^9.3"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": false}}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "ide-helper": ["@php artisan ide-helper:generate -n", "@php artisan ide-helper:meta -n", "@php artisan ide-helper:models -W -n"]}}